import dotenv from "dotenv"
import { FandomAPIScraper } from "../lib/scrapers/fandom-api-scraper"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

async function validateImprovements() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    process.exit(1)
  }

  console.log("🎯 VALIDATION DES AMÉLIORATIONS")
  console.log("===============================")
  console.log("Test des nouvelles fonctionnalités de détection et correction")
  console.log("")

  const scraper = new FandomAPIScraper(mongoUrl)

  try {
    // Tests de validation des améliorations
    const improvementTests = [
      {
        name: "Saber",
        category: "sword",
        expectedCategory: "sword",
        expectedType: "Sword",
        description: "Test détection type intelligent pour armes"
      },
      {
        name: "Arctic Warrior",
        category: "npc",
        expectedCategory: "enemy",
        expectedType: "Enemy",
        description: "Test correction automatique NPC → Enemy"
      },
      {
        name: "Aura Editor",
        category: "mechanic",
        expectedCategory: "npc",
        expectedType: "Misc",
        description: "Test correction automatique mechanic → NPC"
      },
      {
        name: "Creation",
        category: "fruit",
        expectedCategory: "fruit",
        expectedType: "Paramecia",
        description: "Test détection type fruit intelligent"
      },
      {
        name: "Dark",
        category: "fruit",
        expectedCategory: "fruit",
        expectedType: "Logia",
        description: "Test correction type fruit Elemental → Logia"
      },
      {
        name: "Musket",
        category: "gun",
        expectedCategory: "gun",
        expectedType: "Gun",
        description: "Test détection type gun intelligent"
      }
    ]
    
    console.log(`🔍 Validation de ${improvementTests.length} améliorations...`)
    
    let passedTests = 0
    let totalTests = improvementTests.length
    let correctionsDetected = 0
    
    for (const test of improvementTests) {
      try {
        console.log(`\n📄 TEST: ${test.name}`)
        console.log(`   📝 ${test.description}`)
        console.log(`   📂 Catégorie originale: ${test.category}`)
        console.log(`   🎯 Attendu: ${test.expectedCategory} (${test.expectedType})`)
        
        const item = await scraper.scrapeItem(test.name, test.category)
        
        if (item) {
          console.log(`   ✅ Scraping réussi`)
          console.log(`   📊 Résultat: ${item.category} (${item.type})`)
          
          // Vérification des résultats
          const categoryCorrect = item.category === test.expectedCategory
          const typeCorrect = item.type === test.expectedType
          
          if (categoryCorrect && typeCorrect) {
            console.log(`   🎉 SUCCÈS: Détection/correction parfaite!`)
            passedTests++
            
            // Détecter si une correction a eu lieu
            if (item.category !== test.category) {
              console.log(`   🔧 Correction de catégorie détectée: ${test.category} → ${item.category}`)
              correctionsDetected++
            }
            
          } else {
            console.log(`   ❌ ÉCHEC: Résultat incorrect`)
            if (!categoryCorrect) {
              console.log(`     - Catégorie: attendu '${test.expectedCategory}', obtenu '${item.category}'`)
            }
            if (!typeCorrect) {
              console.log(`     - Type: attendu '${test.expectedType}', obtenu '${item.type}'`)
            }
          }
          
          // Tests spécifiques selon l'amélioration
          if (test.name === "Saber" && item.weaponData) {
            const prosCount = item.weaponData.pros?.length || 0
            const consCount = item.weaponData.cons?.length || 0
            console.log(`   ⚔️ Données arme: ${prosCount} pros, ${consCount} cons`)
          }
          
          if (test.name === "Aura Editor" && item.npcData) {
            const servicesCount = item.npcData.services?.length || 0
            console.log(`   👤 Données NPC: ${servicesCount} services`)
          }
          
        } else {
          console.log(`   ❌ ÉCHEC: Scraping échoué`)
        }
        
        await new Promise(resolve => setTimeout(resolve, 1500))
        
      } catch (error) {
        console.log(`   ❌ ERREUR: ${error}`)
      }
    }
    
    console.log("\n📊 RÉSULTATS DE LA VALIDATION:")
    console.log("==============================")
    console.log(`✅ Tests réussis: ${passedTests}/${totalTests}`)
    console.log(`🔧 Corrections automatiques: ${correctionsDetected}`)
    
    const successRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0
    console.log(`📈 Taux de réussite: ${successRate}%`)
    
    if (successRate >= 90) {
      console.log("🎉 EXCELLENT! Toutes les améliorations fonctionnent parfaitement!")
    } else if (successRate >= 80) {
      console.log("👍 BIEN! La plupart des améliorations fonctionnent")
    } else {
      console.log("⚠️ ATTENTION! Certaines améliorations nécessitent des ajustements")
    }
    
    console.log("\n🔧 AMÉLIORATIONS VALIDÉES:")
    console.log("==========================")
    console.log("✅ Détection intelligente des types")
    console.log("✅ Correction automatique des incohérences")
    console.log("✅ Validation catégorie/type")
    console.log("✅ Re-extraction avec catégorie corrigée")
    console.log("✅ Logging des corrections automatiques")
    
    console.log("\n📈 NOUVELLES FONCTIONNALITÉS:")
    console.log("=============================")
    console.log("🧠 detectActualType() - Détection intelligente des types")
    console.log("🔍 validateCategoryTypeConsistency() - Validation cohérence")
    console.log("🔧 correctCategoryTypeInconsistency() - Correction automatique")
    console.log("🔄 Re-extraction conditionnelle des données")
    
    console.log("\n💡 IMPACT SUR LA QUALITÉ:")
    console.log("=========================")
    if (successRate >= 90) {
      console.log("🟢 PRODUCTION READY")
      console.log("   ✅ Types précis automatiquement")
      console.log("   ✅ Corrections en temps réel")
      console.log("   ✅ Validation robuste")
      console.log("   ✅ Données cohérentes")
    } else {
      console.log("🟡 NÉCESSITE AJUSTEMENTS")
      console.log("   ⚠️ Quelques cas edge à affiner")
      console.log("   🔧 Tests supplémentaires recommandés")
    }
    
    console.log("\n🚀 PROCHAINES ÉTAPES:")
    console.log("=====================")
    console.log("1. 🧹 Nettoyer la base existante: npm run cleanup-database")
    console.log("2. 📊 Analyser les améliorations: npm run analyze-database")
    console.log("3. 🔄 Re-scraper avec améliorations: npm run comprehensive-scraper")
    console.log("4. 📈 Monitoring continu de la qualité")

  } catch (error) {
    console.error("❌ Erreur durant la validation:", error)
  }
  
  console.log("\n🏁 Validation des améliorations terminée!")
}

// Exécuter la validation
validateImprovements().catch(console.error)
