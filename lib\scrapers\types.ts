export interface RateLimiter {
  requests: number[]
  maxRequests: number
  timeWindow: number
}

export interface Move {
  name: string
  key?: string
  damage?: number
  cooldown?: number
  mastery?: number
  energy?: number
  type?: string
  description?: string
}

export interface MaterialData {
  berryTypes?: string[]
  locations?: Array<{ sea: string; location: string; bushes: number }>
  usage?: Array<{ type: string; items: Array<{ name: string; rarity: string; price: string }> }>
  totalRequired?: Record<string, number>
  maxStack?: number
  source?: string
  spawnRate?: string
  despawnTime?: string
}

export interface FruitData {
  awakening?: boolean
  type?: "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
  value?: number
  stockChance?: number
  spawnChance?: number
  transformation?: boolean
  passiveAbilities?: Array<{ name: string; description: string }>
  masteryRequirements?: Record<string, number>
  combatRating?: {
    pvp?: "Excellent" | "Good" | "Average" | "Poor"
    grinding?: "Excellent" | "Good" | "Average" | "Poor"
    raids?: "Excellent" | "Good" | "Average" | "Poor"
  }
  pros?: string[]
  cons?: string[]
  trivia?: string[]
  recommendations?: string[]
  counters?: string[]
  changeHistory?: Array<{ update: string; changes: string[] }>
}

export interface WeaponData {
  damage?: number
  masteryRequired?: number
  upgradeRequirements?: Array<{ material: string; quantity: number }>
  specialAbilities?: string[]
  weaponType?: "Sword" | "Gun"
  version?: number
  dropChance?: number
  price?: number
  levelRequirement?: number
  stats?: Array<{ move: string; damage: number; cooldown: number }>
  pros?: string[]
  cons?: string[]
}

export interface AccessoryData {
  buffs?: Array<{ type: string; value: string; description: string }>
  rarity?: string
  dropSource?: string
  dropChance?: number
  location?: string
  price?: string
  pros?: string[]
  cons?: string[]
  stacksWith?: string[]
  trivia?: string[]
}

export interface MechanicData {
  purpose?: string
  triggeredBy?: string
  mechanics?: string[]
  notes?: string[]
  restrictions?: string[]
  trivia?: string[]
}

export interface NPCData {
  npcType?: "Quest" | "Shop" | "Misc" | "Boss" | "Enemy"
  sea?: number
  location?: string
  questRequirements?: Array<{ type: string; description: string; amount?: number }>
  questRewards?: Array<{ type: string; description: string; amount?: number }>
  questSteps?: string[]
  dialogue?: string[]
  cost?: number
  services?: string[]
}

export interface QuestData {
  questGiver?: string
  requirements?: Array<{ type: string; description: string; amount?: number }>
  rewards?: Array<{ type: string; description: string; amount?: number }>
  steps?: string[]
  difficulty?: "Easy" | "Medium" | "Hard" | "Extreme"
  estimatedTime?: string
  tips?: string[]
}

export interface EnemyData {
  enemyType?: "Raid" | "Boss" | "Regular" | "Elite"
  hp?: number
  level?: number
  baseAttack?: number
  attacks?: Array<{ name: string; description: string; howToAvoid: string }>
  immunity?: string[]
  aura?: boolean
  weapon?: string
  spawnLocation?: string[]
  behavior?: string
}

export interface RawData {
  infobox?: Record<string, string>
  movesRaw?: string
  descriptionRaw?: string
  fullWikitextSample?: string
  wikitextLength: number
  movesFound: number
  statsFound: number
  extractedAt: string
  materialData?: any
  fruitData?: any
  weaponData?: any
  accessoryData?: any
  mechanicData?: any
  npcData?: any
  questData?: any
  enemyData?: any
}

export interface ScrapedItem {
  name: string
  type: string
  category: string
  rarity?: string
  price?: string
  robuxPrice?: string
  description?: string
  stats?: string[]
  moves?: Move[]
  imageUrl?: string
  imageUrls?: string[]
  wikiUrl: string
  lastUpdated: Date
  obtainment?: string
  upgrading?: string
  location?: string
  requirements?: string[]
  rewards?: string[]
  hp?: number
  level?: number
  materialData?: MaterialData
  fruitData?: FruitData
  weaponData?: WeaponData
  accessoryData?: AccessoryData
  mechanicData?: MechanicData
  npcData?: NPCData
  questData?: QuestData
  enemyData?: EnemyData
  rawData: RawData
}

export interface CategoryConfig {
  name: string
  type: string
  collection: string
}

export interface ScraperConfig {
  mongoUrl: string
  baseUrl?: string
  rateLimiter?: {
    maxRequests?: number
    timeWindow?: number
  }
}
