import { BaseScraper } from "../base-scraper"
import { ScrapedItem, FruitData } from "../types"

export class FruitScraper extends BaseScraper {
  
  extractSpecificData(wikitext: string, infoboxData: Record<string, string>): FruitData {
    const fruitData: any = {}

    // Extract fruit type with validation
    if (infoboxData.type) {
      const validTypes = ["Natural", "Elemental", "Beast", "Zoan", "Logia", "Paramecia"]
      const type = infoboxData.type.trim()
      if (validTypes.includes(type)) {
        fruitData.type = type as "Natural" | "Elemental" | "Beast" | "Zoan" | "Logia" | "Paramecia"
      }
    }

    // Extract awakening information
    if (infoboxData.awakening) {
      fruitData.awakening = infoboxData.awakening.toLowerCase() === 'yes' || infoboxData.awakening.toLowerCase() === 'available'
    }

    // Extract fruit value
    if (infoboxData.value || infoboxData.money || infoboxData.price) {
      const valueStr = infoboxData.value || infoboxData.money || infoboxData.price
      const value = this.parseNumber(valueStr)
      if (value) fruitData.value = value
    }

    // Extract spawn and stock chances
    if (infoboxData.stock_chance || infoboxData.stockchance) {
      const stockChance = this.parseNumber(infoboxData.stock_chance || infoboxData.stockchance)
      if (stockChance) fruitData.stockChance = stockChance
    }

    if (infoboxData.spawn_chance || infoboxData.spawnchance) {
      const spawnChance = this.parseNumber(infoboxData.spawn_chance || infoboxData.spawnchance)
      if (spawnChance) fruitData.spawnChance = spawnChance
    }

    // Detect transformation
    if (wikitext.toLowerCase().includes("transformation") || wikitext.toLowerCase().includes("transform")) {
      fruitData.transformation = true
    }

    // Extract passive abilities
    const passiveMatch = wikitext.match(/Passive=([\s\S]*?)(?=\|-\||<\/tabber>)/i)
    if (passiveMatch) {
      const passiveAbilities: Array<{ name: string; description: string }> = []
      const passiveContent = passiveMatch[1]

      // Extract table rows for passives
      const rows = passiveContent.split('|-').slice(1)

      rows.forEach(row => {
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell && !cell.startsWith('{'))
        if (cells.length >= 2) {
          const name = this.cleanWikitext(cells[0])
          const description = this.cleanWikitext(cells[1])
          if (name && description && name.length > 2 && description.length > 10) {
            passiveAbilities.push({ name, description })
          }
        }
      })

      if (passiveAbilities.length > 0) {
        fruitData.passiveAbilities = passiveAbilities
      }
    }

    // Extract mastery requirements from moves
    const masteryRequirements: Record<string, number> = {}
    const skillBoxMatches = wikitext.match(/\{\{SkillBox\|[^}]*Mas\s*=\s*(\d+)[^}]*Move\s*=\s*([^|}]+)/g)
    if (skillBoxMatches) {
      skillBoxMatches.forEach(match => {
        const masteryMatch = match.match(/Mas\s*=\s*(\d+)/)
        const moveMatch = match.match(/Move\s*=\s*([^|}]+)/)
        if (masteryMatch && moveMatch) {
          const mastery = parseInt(masteryMatch[1])
          const moveName = this.cleanWikitext(moveMatch[1])
          if (!isNaN(mastery) && moveName) {
            masteryRequirements[moveName] = mastery
          }
        }
      })
    }

    if (Object.keys(masteryRequirements).length > 0) {
      fruitData.masteryRequirements = masteryRequirements
    }

    // Extract pros and cons
    const prosMatch = wikitext.match(/Pros=[\s\S]*?Normal=([\s\S]*?)(?=\{\{\!\}\}-\{\{\!\}\}|Transformed=)/i)
    if (prosMatch) {
      const pros: string[] = []
      const prosContent = prosMatch[1]
      const prosLines = prosContent.split('\n').filter(line => line.trim().startsWith('*'))

      prosLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          pros.push(cleanLine)
        }
      })

      if (pros.length > 0) {
        fruitData.pros = pros.slice(0, 10) // Limit to 10 pros
      }
    }

    const consMatch = wikitext.match(/Cons=[\s\S]*?Normal=([\s\S]*?)(?=\{\{\!\}\}-\{\{\!\}\}|Transformed=)/i)
    if (consMatch) {
      const cons: string[] = []
      const consContent = consMatch[1]
      const consLines = consContent.split('\n').filter(line => line.trim().startsWith('*'))

      consLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 10) {
          cons.push(cleanLine)
        }
      })

      if (cons.length > 0) {
        fruitData.cons = cons.slice(0, 10) // Limit to 10 cons
      }
    }

    // Evaluate combat ratings based on content
    const combatRating: any = {}

    // PvP rating
    if (wikitext.toLowerCase().includes("very good option for pvp") ||
        wikitext.toLowerCase().includes("excellent for pvp")) {
      combatRating.pvp = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for pvp")) {
      combatRating.pvp = "Good"
    }

    // Grinding rating
    if (wikitext.toLowerCase().includes("very good for grinding") ||
        wikitext.toLowerCase().includes("excellent for grinding")) {
      combatRating.grinding = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for grinding")) {
      combatRating.grinding = "Good"
    } else if (wikitext.toLowerCase().includes("decent for grinding")) {
      combatRating.grinding = "Average"
    }

    // Raids rating
    if (wikitext.toLowerCase().includes("extremely good for raids")) {
      combatRating.raids = "Excellent"
    } else if (wikitext.toLowerCase().includes("good for raids")) {
      combatRating.raids = "Good"
    }

    if (Object.keys(combatRating).length > 0) {
      fruitData.combatRating = combatRating
    }

    // Extract trivia information
    const triviaMatch = wikitext.match(/==\s*Trivia\s*==([\s\S]*?)(?===|$)/i)
    if (triviaMatch) {
      const trivia: string[] = []
      const triviaContent = triviaMatch[1]
      const triviaLines = triviaContent.split('\n').filter(line => line.trim().startsWith('*'))

      triviaLines.forEach(line => {
        const cleanLine = this.cleanWikitext(line.replace('*', '').trim())
        if (cleanLine.length > 15 && !cleanLine.toLowerCase().includes('category:')) {
          trivia.push(cleanLine)
        }
      })

      if (trivia.length > 0) {
        fruitData.trivia = trivia.slice(0, 15) // Limit to 15 trivia
      }
    }

    // Extract race/style recommendations
    const recommendations: string[] = []
    const raceRecommendations = wikitext.match(/It is recommended to use \[\[([^\]]+)\]\]/gi)
    if (raceRecommendations) {
      raceRecommendations.forEach(match => {
        const raceMatch = match.match(/\[\[([^\]]+)\]\]/)
        if (raceMatch) {
          const race = this.cleanWikitext(raceMatch[1])
          if (race && !recommendations.includes(race)) {
            recommendations.push(race)
          }
        }
      })
    }

    // Extract general recommendations
    const generalRecommendations = wikitext.match(/recommended[^.]*\./gi)
    if (generalRecommendations) {
      generalRecommendations.forEach(rec => {
        const cleanRec = this.cleanWikitext(rec)
        if (cleanRec.length > 20 && cleanRec.length < 200) {
          recommendations.push(cleanRec)
        }
      })
    }

    if (recommendations.length > 0) {
      fruitData.recommendations = [...new Set(recommendations)].slice(0, 8) // Remove duplicates, limit to 8
    }

    // Extract counter information
    const counters: string[] = []
    const counterMatches = wikitext.match(/good counter to[^.]*\./gi)
    if (counterMatches) {
      counterMatches.forEach(counter => {
        const cleanCounter = this.cleanWikitext(counter)
        if (cleanCounter.length > 10) {
          counters.push(cleanCounter)
        }
      })
    }

    // Extract fruits mentioned as counters
    const fruitCounters = wikitext.match(/counter to fruits like \[\[([^\]]+)\]\]/gi)
    if (fruitCounters) {
      fruitCounters.forEach(match => {
        const fruits = match.match(/\[\[([^\]]+)\]\]/g)
        if (fruits) {
          fruits.forEach(fruit => {
            const fruitName = fruit.replace(/\[\[|\]\]/g, '')
            counters.push(`Counters ${fruitName}`)
          })
        }
      })
    }

    if (counters.length > 0) {
      fruitData.counters = [...new Set(counters)].slice(0, 5) // Remove duplicates, limit to 5
    }

    // Extract change history
    const changeHistoryMatch = wikitext.match(/==\s*Change History\s*==([\s\S]*?)(?===|$)/i)
    if (changeHistoryMatch) {
      const changeHistory: Array<{ update: string; changes: string[] }> = []
      const historyContent = changeHistoryMatch[1]

      // Extract updates
      const updateMatches = historyContent.match(/\{\{Update\|([^}]+)\}\}([\s\S]*?)(?=\{\{Update\||----|\}\})/g)
      if (updateMatches) {
        updateMatches.forEach(updateBlock => {
          const updateMatch = updateBlock.match(/\{\{Update\|([^}]+)\}\}/)
          if (updateMatch) {
            const updateNumber = updateMatch[1].trim()
            const changes: string[] = []

            // Extract changes
            const changeLines = updateBlock.split('\n').filter(line => line.trim().startsWith('*'))
            changeLines.forEach(line => {
              const cleanChange = this.cleanWikitext(line.replace('*', '').trim())
              if (cleanChange.length > 5) {
                changes.push(cleanChange)
              }
            })

            if (changes.length > 0) {
              changeHistory.push({
                update: updateNumber,
                changes: changes
              })
            }
          }
        })
      }

      if (changeHistory.length > 0) {
        fruitData.changeHistory = changeHistory
      }
    }

    // Look for additional value information in text
    const valueMatch = wikitext.match(/(?:worth|value|costs?)\s*(?:of\s*)?(?:approximately\s*)?[\$₿]?([\d,]+)/i)
    if (valueMatch && !fruitData.value) {
      const value = this.parseNumber(valueMatch[1])
      if (value) fruitData.value = value
    }

    return fruitData
  }

  async scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null> {
    console.log(`🍎 Scraping fruit: ${title}`)

    const wikitext = await this.getPageContent(title)
    if (!wikitext) {
      console.warn(`⚠️ No content found for ${title}`)
      return null
    }

    // Extract infobox data
    const infoboxData = this.extractTemplateData(wikitext, "Infobox")

    // Extract fruit-specific data
    const fruitData = this.extractSpecificData(wikitext, infoboxData)

    // Create the scraped item
    const item: ScrapedItem = {
      name: title,
      type: itemType,
      category: "fruit",
      wikiUrl: `https://blox-fruits.fandom.com/wiki/${encodeURIComponent(title)}`,
      lastUpdated: new Date(),
      fruitData,
      rawData: {
        infobox: infoboxData,
        wikitextLength: wikitext.length,
        movesFound: 0,
        statsFound: 0,
        extractedAt: new Date().toISOString(),
        fruitData
      }
    }

    // Extract basic properties from infobox
    if (infoboxData.rarity) item.rarity = infoboxData.rarity
    if (infoboxData.price) item.price = infoboxData.price
    if (infoboxData.description) item.description = this.cleanWikitext(infoboxData.description)

    return item
  }

  async scrapeCategory(categoryName: string = "Blox_Fruits"): Promise<ScrapedItem[]> {
    console.log(`\n🎯 Starting to scrape fruits from category: ${categoryName}`)

    const members = await this.getCategoryMembers(categoryName)
    if (members.length === 0) {
      return []
    }

    const items: ScrapedItem[] = []
    let processed = 0

    for (const member of members) {
      try {
        const item = await this.scrapeItem(member.title, "fruit")
        if (item) {
          items.push(item)
        }
        processed++

        // Progress indicator
        if (processed % 5 === 0 || processed === members.length) {
          console.log(
            `📊 Progress: ${processed}/${members.length} (${Math.round((processed / members.length) * 100)}%)`
          )
        }

        // Small delay between items
        await new Promise((resolve) => setTimeout(resolve, 200))
      } catch (error) {
        console.error(`❌ Error scraping fruit ${member.title}:`, error)
      }
    }

    console.log(`✅ Completed fruits: ${items.length}/${members.length} items scraped`)
    return items
  }
}
