#!/usr/bin/env node

import { ScraperCoordinator } from "./scraper-coordinator"
import { FruitScraper } from "./scrapers/fruit-scraper"
import { MaterialScraper } from "./scrapers/material-scraper"
import { WeaponScraper } from "./scrapers/weapon-scraper"

const MONGO_URL = process.env.MONGODB_URI || "mongodb://localhost:27017"

async function main() {
  const args = process.argv.slice(2)
  const command = args[0]?.toLowerCase()

  console.log("🚀 Blox Fruits Modular Scraper")
  console.log("==============================")

  try {
    switch (command) {
      case "all":
        await scrapeAll()
        break
      case "fruits":
        await scrapeFruits()
        break
      case "materials":
        await scrapeMaterials()
        break
      case "swords":
        await scrapeSwords()
        break
      case "guns":
        await scrapeGuns()
        break
      case "weapons":
        await scrapeWeapons()
        break
      case "specific":
        const categories = args.slice(1)
        if (categories.length === 0) {
          console.error("❌ Please specify categories to scrape")
          printUsage()
          process.exit(1)
        }
        await scrapeSpecific(categories)
        break
      default:
        console.error(`❌ Unknown command: ${command}`)
        printUsage()
        process.exit(1)
    }
  } catch (error) {
    console.error("❌ Fatal error:", error)
    process.exit(1)
  }
}

async function scrapeAll() {
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeAll()
}

async function scrapeFruits() {
  console.log("🍎 Scraping fruits only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["fruits"])
}

async function scrapeMaterials() {
  console.log("🧱 Scraping materials only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["materials"])
}

async function scrapeSwords() {
  console.log("⚔️ Scraping swords only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["swords"])
}

async function scrapeGuns() {
  console.log("🔫 Scraping guns only...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["guns"])
}

async function scrapeWeapons() {
  console.log("⚔️🔫 Scraping all weapons (swords + guns)...")
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(["swords", "guns"])
}

async function scrapeSpecific(categories: string[]) {
  const coordinator = new ScraperCoordinator(MONGO_URL)
  await coordinator.scrapeSpecific(categories)
}

// Individual scraper usage examples
async function runIndividualScrapers() {
  console.log("🔧 Running individual scrapers...")

  // Example: Use FruitScraper directly
  const fruitScraper = new FruitScraper()
  const fruits = await fruitScraper.scrapeCategory("Blox_Fruits")
  console.log(`Scraped ${fruits.length} fruits`)

  // Example: Use MaterialScraper directly
  const materialScraper = new MaterialScraper()
  const materials = await materialScraper.scrapeCategory("Materials")
  console.log(`Scraped ${materials.length} materials`)

  // Example: Use WeaponScraper directly
  const weaponScraper = new WeaponScraper()
  const swords = await weaponScraper.scrapeCategory("Swords", "sword")
  const guns = await weaponScraper.scrapeCategory("Guns", "gun")
  console.log(`Scraped ${swords.length} swords and ${guns.length} guns`)
}

function printUsage() {
  console.log(`
Usage: npm run scrape:modular <command> [options]

Commands:
  all                    Scrape all categories (fruits, materials, swords, guns)
  fruits                 Scrape fruits only
  materials              Scrape materials only
  swords                 Scrape swords only
  guns                   Scrape guns only
  weapons                Scrape both swords and guns
  specific <categories>  Scrape specific categories

Examples:
  npm run scrape:modular all
  npm run scrape:modular fruits
  npm run scrape:modular specific fruits materials
  npm run scrape:modular specific swords guns

Environment Variables:
  MONGODB_URI           MongoDB connection string (default: mongodb://localhost:27017)
`)
}

// Handle command line execution
if (require.main === module) {
  main().catch(error => {
    console.error("❌ Unhandled error:", error)
    process.exit(1)
  })
}

export {
  ScraperCoordinator,
  FruitScraper,
  MaterialScraper,
  WeaponScraper
}
