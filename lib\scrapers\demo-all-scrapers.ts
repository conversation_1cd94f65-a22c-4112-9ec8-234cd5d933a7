#!/usr/bin/env node

import { ScraperCoordinator } from "./scraper-coordinator"

const MONGO_URL = process.env.MONGODB_URI || "mongodb://localhost:27017"

async function demonstrateAllScrapers() {
  console.log("🚀 === DÉMONSTRATION COMPLÈTE DES SCRAPERS MODULAIRES ===")
  console.log("Scraping d'un échantillon de chaque catégorie...\n")

  const coordinator = new ScraperCoordinator(MONGO_URL)

  try {
    console.log("📊 === STATISTIQUES DES CATÉGORIES ===")

    // Obtenir les statistiques de chaque catégorie
    const categories = [
      { name: "Blox_Fruits", type: "fruit", emoji: "🍎" },
      { name: "Materials", type: "material", emoji: "🧱" },
      { name: "Swords", type: "sword", emoji: "⚔️" },
      { name: "Guns", type: "gun", emoji: "🔫" },
      { name: "Accessories", type: "accessory", emoji: "💍" },
      { name: "NPCs", type: "npc", emoji: "👤" },
      { name: "Quests", type: "quest", emoji: "📋" },
      { name: "Raids", type: "raid", emoji: "👹" },
      { name: "Game_Mechanics", type: "mechanic", emoji: "⚙️" }
    ]

    let totalItems = 0

    for (const category of categories) {
      try {
        const scraper = getScraper(coordinator, category.type)
        const members = await scraper.getCategoryMembers(category.name)
        totalItems += members.length

        console.log(`${category.emoji} ${category.name.padEnd(15)} : ${members.length.toString().padStart(3)} items`)

        // Petit délai pour éviter le rate limiting
        await new Promise(resolve => setTimeout(resolve, 300))
      } catch (error) {
        console.log(`❌ ${category.name.padEnd(15)} : Erreur`)
      }
    }

    console.log(`\n📈 TOTAL ESTIMÉ: ${totalItems} items dans toutes les catégories`)

    console.log("\n🎯 === DÉMONSTRATION DE SCRAPING SÉLECTIF ===")
    
    // Démonstration de scraping de quelques items de chaque catégorie
    const demonstrations = [
      { category: "fruits", count: 2, emoji: "🍎" },
      { category: "materials", count: 2, emoji: "🧱" },
      { category: "swords", count: 1, emoji: "⚔️" },
      { category: "accessories", count: 1, emoji: "💍" }
    ]

    for (const demo of demonstrations) {
      console.log(`\n${demo.emoji} Scraping ${demo.count} ${demo.category}...`)
      
      try {
        const items = await coordinator.scrapeSpecific([demo.category])
        
        if (items.length > 0) {
          console.log(`✅ Scraped ${items.length} ${demo.category}`)
          
          // Afficher un échantillon des données
          const sample = items[0]
          console.log(`   Exemple: ${sample.name}`)
          console.log(`   URL: ${sample.wikiUrl}`)
          console.log(`   Dernière mise à jour: ${sample.lastUpdated.toISOString()}`)
          
          // Afficher des données spécifiques selon le type
          if (sample.fruitData) {
            console.log(`   Type de fruit: ${sample.fruitData.type || 'N/A'}`)
            console.log(`   Awakening: ${sample.fruitData.awakening ? 'Oui' : 'Non'}`)
          }
          
          if (sample.materialData) {
            console.log(`   Types de berries: ${sample.materialData.berryTypes?.length || 0}`)
            console.log(`   Locations: ${sample.materialData.locations?.length || 0}`)
          }
          
          if (sample.weaponData) {
            console.log(`   Type d'arme: ${sample.weaponData.weaponType || 'N/A'}`)
            console.log(`   Version: ${sample.weaponData.version || 1}`)
          }
          
          if (sample.accessoryData) {
            console.log(`   Rareté: ${sample.accessoryData.rarity || 'N/A'}`)
            console.log(`   Buffs: ${sample.accessoryData.buffs?.length || 0}`)
          }
        } else {
          console.log(`⚠️ Aucun item trouvé pour ${demo.category}`)
        }
        
        // Pause entre les catégories
        await new Promise(resolve => setTimeout(resolve, 2000))
      } catch (error) {
        console.error(`❌ Erreur lors du scraping de ${demo.category}:`, error.message)
      }
    }

    console.log("\n🎉 === DÉMONSTRATION TERMINÉE ===")
    console.log("✅ Architecture modulaire complètement opérationnelle!")
    console.log("🚀 Tous les scrapers sont prêts pour une utilisation en production")
    
    console.log("\n📋 === COMMANDES DISPONIBLES ===")
    console.log("npm run scrape:modular all                    # Scraper toutes les catégories")
    console.log("npm run scrape:modular fruits                # Scraper les fruits uniquement")
    console.log("npm run scrape:modular specific fruits guns  # Scraper des catégories spécifiques")
    console.log("npx tsx lib/scrapers/test-modular.ts         # Tester tous les scrapers")

  } catch (error) {
    console.error("❌ Erreur lors de la démonstration:", error)
  } finally {
    await coordinator.close()
  }
}

// Fonction helper pour obtenir le bon scraper
function getScraper(coordinator: any, type: string) {
  switch (type) {
    case 'fruit': return coordinator.fruitScraper
    case 'material': return coordinator.materialScraper
    case 'sword':
    case 'gun': return coordinator.weaponScraper
    case 'accessory': return coordinator.accessoryScraper
    case 'npc': return coordinator.npcScraper
    case 'quest': return coordinator.questScraper
    case 'raid':
    case 'enemy': return coordinator.enemyScraper
    case 'mechanic': return coordinator.mechanicScraper
    default: throw new Error(`Unknown scraper type: ${type}`)
  }
}

// Handle command line execution
if (require.main === module) {
  demonstrateAllScrapers().catch(error => {
    console.error("❌ Erreur non gérée:", error)
    process.exit(1)
  })
}

export { demonstrateAllScrapers }
