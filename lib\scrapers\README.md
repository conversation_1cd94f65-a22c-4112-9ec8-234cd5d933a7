# Blox Fruits Modular Scraper

Cette architecture modulaire divise le scraper monolithique en plusieurs composants spécialisés, chacun responsable d'une catégorie spécifique d'items.

## Architecture

```
lib/scrapers/
├── types.ts                    # Interfaces et types partagés
├── base-scraper.ts            # Classe de base avec méthodes communes
├── scrapers/
│   ├── fruit-scraper.ts       # Scraping des fruits
│   ├── material-scraper.ts    # Scraping des matériaux  
│   ├── weapon-scraper.ts      # Scraping des armes (swords + guns)
│   ├── accessory-scraper.ts   # Scraping des accessoires (à implémenter)
│   ├── npc-scraper.ts         # Scraping des NPCs (à implémenter)
│   ├── quest-scraper.ts       # Scraping des quêtes (à implémenter)
│   ├── enemy-scraper.ts       # Scraping des raids/ennemis (à implémenter)
│   └── mechanic-scraper.ts    # Scraping des mécaniques (à implémenter)
├── scraper-coordinator.ts     # Orchestrateur principal
├── run-modular-scraper.ts     # Script d'exécution
├── fandom-api-scraper.ts      # Ancien scraper monolithique (à supprimer)
└── README.md                  # Cette documentation
```

## Avantages

### 🔧 Maintenabilité
- Chaque fichier est plus petit et focalisé sur une seule responsabilité
- Plus facile de naviguer et comprendre le code
- Modifications isolées par catégorie

### 🔄 Réutilisabilité
- Possibilité d'utiliser des scrapers individuels
- Facilite les tests unitaires
- Code plus modulaire

### ⚡ Performance
- Possibilité de parallélisation future
- Scraping sélectif par catégorie
- Meilleur contrôle des ressources

### 🧪 Tests
- Plus facile de tester chaque composant individuellement
- Mocking simplifié
- Tests plus ciblés

## Utilisation

### Script principal

```bash
# Scraper toutes les catégories
npm run scrape:modular all

# Scraper une catégorie spécifique
npm run scrape:modular fruits
npm run scrape:modular materials
npm run scrape:modular swords
npm run scrape:modular guns

# Scraper plusieurs catégories
npm run scrape:modular specific fruits materials
npm run scrape:modular weapons  # swords + guns
```

### Utilisation programmatique

```typescript
import { ScraperCoordinator } from "./scraper-coordinator"
import { FruitScraper } from "./scrapers/fruit-scraper"

// Utilisation du coordinateur
const coordinator = new ScraperCoordinator("mongodb://localhost:27017")
await coordinator.scrapeAll()

// Utilisation d'un scraper individuel
const fruitScraper = new FruitScraper()
const fruits = await fruitScraper.scrapeCategory("Blox_Fruits")
```

## Scrapers implémentés

### ✅ FruitScraper
- Extraction des données spécifiques aux fruits
- Types, awakening, valeurs, chances de spawn
- Capacités passives, exigences de maîtrise
- Pros/cons, ratings de combat
- Trivia, recommandations, counters
- Historique des changements

### ✅ MaterialScraper
- Types de berries
- Locations avec nombre de bushes
- Informations d'usage (crafting, upgrading, etc.)
- Totaux requis
- Propriétés additionnelles (source, spawn rate, etc.)

### ✅ WeaponScraper
- Gère à la fois les swords et guns
- Dégâts, maîtrise requise
- Exigences d'amélioration
- Stats des moves
- Pros/cons, capacités spéciales
- Détection de version (V2, V3)

## Scrapers à implémenter

### 🔄 AccessoryScraper
- Buffs et effets
- Rareté, sources de drop
- Compatibilité (stacks with)
- Pros/cons, trivia

### 🔄 NPCScraper
- Types de NPCs (Quest, Shop, Boss, etc.)
- Localisation, mer
- Exigences et récompenses de quêtes
- Services, dialogue

### 🔄 QuestScraper
- Donneurs de quêtes
- Exigences et récompenses
- Étapes, difficulté
- Temps estimé, conseils

### 🔄 EnemyScraper
- Types d'ennemis (Raid, Boss, etc.)
- HP, niveau, attaque de base
- Attaques et comment les éviter
- Immunités, aura, armes

### 🔄 MechanicScraper
- Purpose, déclencheurs
- Mécaniques de jeu
- Notes, restrictions
- Trivia

## Configuration

### Variables d'environnement

```bash
MONGODB_URI=mongodb://localhost:27017  # URL de la base de données
```

### Rate Limiting

Chaque scraper hérite du rate limiting de BaseScraper :
- 90 requêtes par minute maximum
- Fenêtre glissante de 60 secondes
- Pause automatique si limite atteinte

## Migration depuis l'ancien scraper

L'ancien fichier `fandom-api-scraper.ts` peut être supprimé une fois que tous les scrapers sont implémentés et testés. Les données extraites sont compatibles avec l'ancien format.

## Développement

### Ajouter un nouveau scraper

1. Créer une classe héritant de `BaseScraper`
2. Implémenter `extractSpecificData()` et `scrapeItem()`
3. Ajouter les types spécifiques dans `types.ts`
4. Intégrer dans `ScraperCoordinator`
5. Mettre à jour le script d'exécution

### Tests

```bash
# Tester un scraper individuel
npm run test:scraper fruit
npm run test:scraper material

# Tester le coordinateur
npm run test:coordinator
```

## Monitoring

Chaque scraper fournit des logs détaillés :
- 🔍 Recherche de membres de catégorie
- 📊 Progression du scraping
- ✅ Résultats et statistiques
- ❌ Erreurs et avertissements
- 💾 Sauvegarde en base de données
