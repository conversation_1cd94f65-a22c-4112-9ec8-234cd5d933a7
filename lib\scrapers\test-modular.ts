#!/usr/bin/env node

import { FruitScraper } from "./scrapers/fruit-scraper"
import { MaterialScraper } from "./scrapers/material-scraper"
import { WeaponScraper } from "./scrapers/weapon-scraper"
import { AccessoryScraper } from "./scrapers/accessory-scraper"
import { NPCScraper } from "./scrapers/npc-scraper"
import { QuestScraper } from "./scrapers/quest-scraper"
import { EnemyScraper } from "./scrapers/enemy-scraper"
import { MechanicScraper } from "./scrapers/mechanic-scraper"

async function testFruitScraper() {
  console.log("\n🍎 === TESTING FRUIT SCRAPER ===")
  
  const scraper = new FruitScraper()
  
  try {
    // Test scraping a single fruit
    console.log("Testing single fruit scraping...")
    const fruit = await scraper.scrapeItem("Venom", "fruit")
    
    if (fruit) {
      console.log("✅ Successfully scraped Venom fruit")
      console.log(`   Name: ${fruit.name}`)
      console.log(`   Type: ${fruit.fruitData?.type}`)
      console.log(`   Awakening: ${fruit.fruitData?.awakening}`)
      console.log(`   Value: ${fruit.fruitData?.value}`)
      console.log(`   Pros: ${fruit.fruitData?.pros?.length || 0}`)
      console.log(`   Cons: ${fruit.fruitData?.cons?.length || 0}`)
      console.log(`   Trivia: ${fruit.fruitData?.trivia?.length || 0}`)
    } else {
      console.log("❌ Failed to scrape Venom fruit")
    }
    
    // Test getting category members
    console.log("\nTesting category members...")
    const members = await scraper.getCategoryMembers("Blox_Fruits")
    console.log(`✅ Found ${members.length} fruits in category`)
    
    if (members.length > 0) {
      console.log(`   First few fruits: ${members.slice(0, 3).map(m => m.title).join(", ")}`)
    }
    
  } catch (error) {
    console.error("❌ Error testing fruit scraper:", error)
  }
}

async function testMaterialScraper() {
  console.log("\n🧱 === TESTING MATERIAL SCRAPER ===")
  
  const scraper = new MaterialScraper()
  
  try {
    // Test scraping a single material
    console.log("Testing single material scraping...")
    const material = await scraper.scrapeItem("Berries", "material")
    
    if (material) {
      console.log("✅ Successfully scraped Berries material")
      console.log(`   Name: ${material.name}`)
      console.log(`   Berry types: ${material.materialData?.berryTypes?.length || 0}`)
      console.log(`   Locations: ${material.materialData?.locations?.length || 0}`)
      console.log(`   Usage sections: ${material.materialData?.usage?.length || 0}`)
      console.log(`   Total required: ${Object.keys(material.materialData?.totalRequired || {}).length}`)
    } else {
      console.log("❌ Failed to scrape Berries material")
    }
    
    // Test getting category members
    console.log("\nTesting category members...")
    const members = await scraper.getCategoryMembers("Materials")
    console.log(`✅ Found ${members.length} materials in category`)
    
    if (members.length > 0) {
      console.log(`   First few materials: ${members.slice(0, 3).map(m => m.title).join(", ")}`)
    }
    
  } catch (error) {
    console.error("❌ Error testing material scraper:", error)
  }
}

async function testWeaponScraper() {
  console.log("\n⚔️ === TESTING WEAPON SCRAPER ===")
  
  const scraper = new WeaponScraper()
  
  try {
    // Test scraping a sword
    console.log("Testing sword scraping...")
    const sword = await scraper.scrapeItem("Saber", "sword")
    
    if (sword) {
      console.log("✅ Successfully scraped Saber sword")
      console.log(`   Name: ${sword.name}`)
      console.log(`   Weapon type: ${sword.weaponData?.weaponType}`)
      console.log(`   Damage: ${sword.weaponData?.damage}`)
      console.log(`   Mastery required: ${sword.weaponData?.masteryRequired}`)
      console.log(`   Version: ${sword.weaponData?.version}`)
      console.log(`   Pros: ${sword.weaponData?.pros?.length || 0}`)
      console.log(`   Cons: ${sword.weaponData?.cons?.length || 0}`)
      console.log(`   Stats: ${sword.weaponData?.stats?.length || 0}`)
    } else {
      console.log("❌ Failed to scrape Saber sword")
    }
    
    // Test getting sword category members
    console.log("\nTesting sword category members...")
    const swordMembers = await scraper.getCategoryMembers("Swords")
    console.log(`✅ Found ${swordMembers.length} swords in category`)
    
    if (swordMembers.length > 0) {
      console.log(`   First few swords: ${swordMembers.slice(0, 3).map(m => m.title).join(", ")}`)
    }
    
    // Test getting gun category members
    console.log("\nTesting gun category members...")
    const gunMembers = await scraper.getCategoryMembers("Guns")
    console.log(`✅ Found ${gunMembers.length} guns in category`)
    
    if (gunMembers.length > 0) {
      console.log(`   First few guns: ${gunMembers.slice(0, 3).map(m => m.title).join(", ")}`)
    }
    
  } catch (error) {
    console.error("❌ Error testing weapon scraper:", error)
  }
}

async function testBaseScraper() {
  console.log("\n🔧 === TESTING BASE SCRAPER FUNCTIONALITY ===")
  
  const scraper = new FruitScraper()
  
  try {
    // Test rate limiting
    console.log("Testing rate limiting...")
    const start = Date.now()
    
    // Make multiple requests quickly
    const promises = []
    for (let i = 0; i < 5; i++) {
      promises.push(scraper.getCategoryMembers("Blox_Fruits"))
    }
    
    await Promise.all(promises)
    const elapsed = Date.now() - start
    console.log(`✅ Made 5 requests in ${elapsed}ms (rate limiting working)`)
    
    // Test template extraction
    console.log("\nTesting template extraction...")
    const testWikitext = `
    {{Infobox
    |name = Test Fruit
    |type = Logia
    |price = 1000000
    |awakening = Yes
    }}
    Some content here
    `
    
    const templateData = scraper.extractTemplateData(testWikitext, "Infobox")
    console.log("✅ Template extraction working")
    console.log(`   Extracted keys: ${Object.keys(templateData).join(", ")}`)
    
    // Test text cleaning
    console.log("\nTesting text cleaning...")
    const dirtyText = "[[Link|Display Text]] {{Template}} <tag>content</tag>   extra   spaces   "
    const cleanText = scraper.cleanWikitext(dirtyText)
    console.log(`✅ Text cleaning: "${dirtyText}" -> "${cleanText}"`)
    
  } catch (error) {
    console.error("❌ Error testing base scraper:", error)
  }
}

async function testAccessoryScraper() {
  console.log("\n💍 === TESTING ACCESSORY SCRAPER ===")

  const scraper = new AccessoryScraper()

  try {
    // Test getting category members
    console.log("Testing accessory category members...")
    const members = await scraper.getCategoryMembers("Accessories")
    console.log(`✅ Found ${members.length} accessories in category`)

    if (members.length > 0) {
      console.log(`   First few accessories: ${members.slice(0, 3).map(m => m.title).join(", ")}`)

      // Test scraping a single accessory
      console.log("Testing single accessory scraping...")
      const accessory = await scraper.scrapeItem(members[0].title, "accessory")

      if (accessory) {
        console.log("✅ Successfully scraped accessory")
        console.log(`   Name: ${accessory.name}`)
        console.log(`   Rarity: ${accessory.accessoryData?.rarity}`)
        console.log(`   Buffs: ${accessory.accessoryData?.buffs?.length || 0}`)
        console.log(`   Pros: ${accessory.accessoryData?.pros?.length || 0}`)
        console.log(`   Cons: ${accessory.accessoryData?.cons?.length || 0}`)
      } else {
        console.log("❌ Failed to scrape accessory")
      }
    }

  } catch (error) {
    console.error("❌ Error testing accessory scraper:", error)
  }
}

async function testNPCScraper() {
  console.log("\n👤 === TESTING NPC SCRAPER ===")

  const scraper = new NPCScraper()

  try {
    // Test getting category members
    console.log("Testing NPC category members...")
    const members = await scraper.getCategoryMembers("NPCs")
    console.log(`✅ Found ${members.length} NPCs in category`)

    if (members.length > 0) {
      console.log(`   First few NPCs: ${members.slice(0, 3).map(m => m.title).join(", ")}`)

      // Test scraping a single NPC
      console.log("Testing single NPC scraping...")
      const npc = await scraper.scrapeItem(members[0].title, "npc")

      if (npc) {
        console.log("✅ Successfully scraped NPC")
        console.log(`   Name: ${npc.name}`)
        console.log(`   Type: ${npc.npcData?.npcType}`)
        console.log(`   Sea: ${npc.npcData?.sea}`)
        console.log(`   Services: ${npc.npcData?.services?.length || 0}`)
        console.log(`   Quest requirements: ${npc.npcData?.questRequirements?.length || 0}`)
      } else {
        console.log("❌ Failed to scrape NPC")
      }
    }

  } catch (error) {
    console.error("❌ Error testing NPC scraper:", error)
  }
}

async function testQuestScraper() {
  console.log("\n📋 === TESTING QUEST SCRAPER ===")

  const scraper = new QuestScraper()

  try {
    // Test getting category members
    console.log("Testing quest category members...")
    const members = await scraper.getCategoryMembers("Quests")
    console.log(`✅ Found ${members.length} quests in category`)

    if (members.length > 0) {
      console.log(`   First few quests: ${members.slice(0, 3).map(m => m.title).join(", ")}`)

      // Test scraping a single quest
      console.log("Testing single quest scraping...")
      const quest = await scraper.scrapeItem(members[0].title, "quest")

      if (quest) {
        console.log("✅ Successfully scraped quest")
        console.log(`   Name: ${quest.name}`)
        console.log(`   Quest giver: ${quest.questData?.questGiver}`)
        console.log(`   Difficulty: ${quest.questData?.difficulty}`)
        console.log(`   Requirements: ${quest.questData?.requirements?.length || 0}`)
        console.log(`   Rewards: ${quest.questData?.rewards?.length || 0}`)
        console.log(`   Steps: ${quest.questData?.steps?.length || 0}`)
      } else {
        console.log("❌ Failed to scrape quest")
      }
    }

  } catch (error) {
    console.error("❌ Error testing quest scraper:", error)
  }
}

async function testEnemyScraper() {
  console.log("\n👹 === TESTING ENEMY SCRAPER ===")

  const scraper = new EnemyScraper()

  try {
    // Test getting category members
    console.log("Testing enemy category members...")
    const members = await scraper.getCategoryMembers("Raids")
    console.log(`✅ Found ${members.length} enemies/raids in category`)

    if (members.length > 0) {
      console.log(`   First few enemies: ${members.slice(0, 3).map(m => m.title).join(", ")}`)

      // Test scraping a single enemy
      console.log("Testing single enemy scraping...")
      const enemy = await scraper.scrapeItem(members[0].title, "enemy")

      if (enemy) {
        console.log("✅ Successfully scraped enemy")
        console.log(`   Name: ${enemy.name}`)
        console.log(`   Type: ${enemy.enemyData?.enemyType}`)
        console.log(`   HP: ${enemy.enemyData?.hp}`)
        console.log(`   Level: ${enemy.enemyData?.level}`)
        console.log(`   Attacks: ${enemy.enemyData?.attacks?.length || 0}`)
        console.log(`   Immunities: ${enemy.enemyData?.immunity?.length || 0}`)
      } else {
        console.log("❌ Failed to scrape enemy")
      }
    }

  } catch (error) {
    console.error("❌ Error testing enemy scraper:", error)
  }
}

async function testMechanicScraper() {
  console.log("\n⚙️ === TESTING MECHANIC SCRAPER ===")

  const scraper = new MechanicScraper()

  try {
    // Test getting category members
    console.log("Testing mechanic category members...")
    const members = await scraper.getCategoryMembers("Game_Mechanics")
    console.log(`✅ Found ${members.length} mechanics in category`)

    if (members.length > 0) {
      console.log(`   First few mechanics: ${members.slice(0, 3).map(m => m.title).join(", ")}`)

      // Test scraping a single mechanic
      console.log("Testing single mechanic scraping...")
      const mechanic = await scraper.scrapeItem(members[0].title, "mechanic")

      if (mechanic) {
        console.log("✅ Successfully scraped mechanic")
        console.log(`   Name: ${mechanic.name}`)
        console.log(`   Purpose: ${mechanic.mechanicData?.purpose}`)
        console.log(`   Triggered by: ${mechanic.mechanicData?.triggeredBy}`)
        console.log(`   Mechanics: ${mechanic.mechanicData?.mechanics?.length || 0}`)
        console.log(`   Notes: ${mechanic.mechanicData?.notes?.length || 0}`)
      } else {
        console.log("❌ Failed to scrape mechanic")
      }
    }

  } catch (error) {
    console.error("❌ Error testing mechanic scraper:", error)
  }
}

async function runAllTests() {
  console.log("🧪 === MODULAR SCRAPER TESTS ===")
  console.log("Testing the complete modular architecture...")

  try {
    await testBaseScraper()
    await testFruitScraper()
    await testMaterialScraper()
    await testWeaponScraper()
    await testAccessoryScraper()
    await testNPCScraper()
    await testQuestScraper()
    await testEnemyScraper()
    await testMechanicScraper()

    console.log("\n🎉 === ALL TESTS COMPLETED ===")
    console.log("✅ Complete modular architecture is working correctly!")
    console.log("🚀 All 9 scrapers (Base + 8 specialized) are operational!")

  } catch (error) {
    console.error("❌ Test suite failed:", error)
    process.exit(1)
  }
}

// Handle command line execution
if (require.main === module) {
  runAllTests().catch(error => {
    console.error("❌ Unhandled error:", error)
    process.exit(1)
  })
}

export { runAllTests }
