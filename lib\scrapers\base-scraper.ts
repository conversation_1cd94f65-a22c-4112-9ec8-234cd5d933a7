import fetch from "node-fetch"
import { RateLimiter, ScrapedItem } from "./types"

export abstract class BaseScraper {
  protected baseUrl = "https://blox-fruits.fandom.com/api.php"
  protected rateLimiter: RateLimiter

  constructor() {
    this.rateLimiter = {
      requests: [],
      maxRequests: 90, // 90 requests per minute to be safe
      timeWindow: 60000, // 1 minute
    }
  }

  protected async waitForRateLimit(): Promise<void> {
    const now = Date.now()
    this.rateLimiter.requests = this.rateLimiter.requests.filter((time: number) => now - time < this.rateLimiter.timeWindow)

    if (this.rateLimiter.requests.length >= this.rateLimiter.maxRequests) {
      const waitTime = this.rateLimiter.timeWindow - (now - this.rateLimiter.requests[0]) + 1000 // Add 1s buffer
      console.log(`⏳ Rate limit reached, waiting ${Math.ceil(waitTime / 1000)}s...`)
      await new Promise((resolve) => setTimeout(resolve, waitTime))
    }

    this.rateLimiter.requests.push(now)
  }

  protected async makeApiRequest(params: Record<string, string>): Promise<any> {
    await this.waitForRateLimit()

    const url = `${this.baseUrl}?${new URLSearchParams(params)}`
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText} for ${url}`)
    }

    return await response.json()
  }

  async getCategoryMembers(categoryName: string): Promise<Array<{ title: string; pageid: number }>> {
    console.log(`🔍 Fetching members of category: ${categoryName}`)

    const params = {
      action: "query",
      list: "categorymembers",
      cmtitle: `Category:${categoryName}`,
      cmlimit: "500", // Max limit
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)

      if (!data.query?.categorymembers) {
        console.warn(`⚠️ No members found for category: ${categoryName}`)
        return []
      }

      const members = data.query.categorymembers
      console.log(`✅ Found ${members.length} members in ${categoryName}`)
      return members
    } catch (error) {
      console.error(`❌ Error fetching category ${categoryName}:`, error)
      return []
    }
  }

  async getPageContent(title: string): Promise<string | null> {
    const params = {
      action: "query",
      titles: title,
      prop: "revisions",
      rvprop: "content",
      format: "json",
    }

    try {
      const data = await this.makeApiRequest(params)
      const pages = data.query?.pages

      if (!pages) return null

      const pageId = Object.keys(pages)[0]
      const page = pages[pageId]

      if (page.missing || !page.revisions) {
        // Handle redirects
        const wikitextContent = page.revisions?.[0]?.["*"];
        if (wikitextContent && wikitextContent.toUpperCase().startsWith("#REDIRECT")) {
          const redirectTargetMatch = wikitextContent.match(/#REDIRECT\s*\[\[([^\]]+)\]\]/i)
          if (redirectTargetMatch && redirectTargetMatch[1]) {
            console.log(`🔄 Redirect found for "${title}", redirecting to "${redirectTargetMatch[1]}"`)
            return this.getPageContent(redirectTargetMatch[1])
          }
        }
        return null
      }

      return page.revisions[0]["*"]
    } catch (error) {
      console.error(`❌ Error fetching content for ${title}:`, error)
      return null
    }
  }

  protected extractTemplateData(wikitext: string, templateName: string): Record<string, string> {
    const data: Record<string, string> = {}

    // Escape special regex characters in templateName
    const escapedTemplateName = templateName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

    // Pattern for finding the template specifically
    const templatePattern = new RegExp(`\\{\\{\\s*${escapedTemplateName}\\s*\\|([\\s\\S]*?)\\}\\}`, "i")
    const match = wikitext.match(templatePattern)

    if (!match) {
      return data
    }

    const templateContent = match[1]

    // Extract template parameters
    const paramPattern = /\|\s*([^=|]+)\s*=\s*([\s\S]*?)(?=\|\s*\w+\s*=|\|\s*\}\}|\n\}\})/g
    let paramMatch

    while ((paramMatch = paramPattern.exec(templateContent)) !== null) {
      const key = paramMatch[1].trim().toLowerCase()
      const value = paramMatch[2].trim()

      if (value && value !== "") {
        data[key] = this.cleanWikitext(value)
      }
    }

    return data
  }

  protected cleanWikitext(text: string): string {
    if (!text) return ""

    return text
      // Remove wiki links but keep the display text
      .replace(/\[\[([^|\]]+)\|([^\]]+)\]\]/g, "$2")
      .replace(/\[\[([^\]]+)\]\]/g, "$1")
      // Remove templates
      .replace(/\{\{[^}]*\}\}/g, "")
      // Remove HTML tags
      .replace(/<[^>]*>/g, "")
      // Remove extra whitespace
      .replace(/\s+/g, " ")
      .trim()
  }

  protected parseNumber(str: string): number | undefined {
    if (!str) return undefined
    
    const cleaned = str.replace(/[^\d.]/g, "")
    const num = parseFloat(cleaned)
    return isNaN(num) ? undefined : num
  }

  // Abstract methods that must be implemented by subclasses
  abstract extractSpecificData(wikitext: string, infoboxData: Record<string, string>): any
  abstract scrapeItem(title: string, itemType: string): Promise<ScrapedItem | null>
}
