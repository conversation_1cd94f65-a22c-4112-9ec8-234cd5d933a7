import dotenv from "dotenv"
import { MongoClient } from "mongodb"

// Charger les variables d'environnement
dotenv.config({ path: ".env.local" })

interface DatabaseItem {
  _id: any
  name: string
  type: string
  category: string
  [key: string]: any
}

async function cleanupDatabase() {
  const mongoUrl = process.env.MONGODB_URL
  if (!mongoUrl) {
    console.error("❌ MONGODB_URL environment variable is required")
    process.exit(1)
  }

  console.log("🧹 NETTOYAGE DE LA BASE DE DONNÉES")
  console.log("==================================")
  console.log("Correction automatique des incohérences détectées")
  console.log("")

  const client = new MongoClient(mongoUrl)

  try {
    await client.connect()
    const db = client.db("bloxfruits")
    
    // Obtenir toutes les collections avec des données
    const collections = await db.listCollections().toArray()
    const collectionsWithData = []
    
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments()
      if (count > 0 && collection.name !== 'scraping_stats') {
        collectionsWithData.push({
          name: collection.name,
          count: count,
          collection: db.collection(collection.name)
        })
      }
    }
    
    console.log(`📊 Collections à nettoyer: ${collectionsWithData.length}`)
    
    let totalCorrections = 0
    let totalItems = 0
    
    for (const collectionInfo of collectionsWithData) {
      console.log(`\n🔍 Nettoyage de ${collectionInfo.name}...`)
      
      const items = await collectionInfo.collection.find({}).toArray()
      let collectionCorrections = 0
      
      for (const item of items) {
        totalItems++
        let needsUpdate = false
        const updates: any = {}
        
        // 1. Correction des types "Unknown"
        if (item.type === "Unknown") {
          const correctedType = correctType(item.category, item.name)
          if (correctedType !== "Unknown") {
            updates.type = correctedType
            needsUpdate = true
            console.log(`   🔧 ${item.name}: type Unknown → ${correctedType}`)
          }
        }
        
        // 2. Correction des incohérences catégorie/type
        const categoryCorrection = correctCategoryType(item.category, item.type, item.name)
        if (categoryCorrection.corrected) {
          if (categoryCorrection.category !== item.category) {
            updates.category = categoryCorrection.category
            needsUpdate = true
            console.log(`   🔧 ${item.name}: catégorie ${item.category} → ${categoryCorrection.category}`)
          }
          if (categoryCorrection.type !== item.type) {
            updates.type = categoryCorrection.type
            needsUpdate = true
            console.log(`   🔧 ${item.name}: type ${item.type} → ${categoryCorrection.type}`)
          }
        }
        
        // 3. Déplacer les items mal placés vers les bonnes collections
        const correctCollection = getCorrectCollection(item.category)
        if (correctCollection !== collectionInfo.name) {
          console.log(`   📦 ${item.name}: déplacement ${collectionInfo.name} → ${correctCollection}`)
          
          // Ajouter à la bonne collection
          const targetCollection = db.collection(correctCollection)
          await targetCollection.insertOne(item)
          
          // Supprimer de la collection actuelle
          await collectionInfo.collection.deleteOne({ _id: item._id })
          
          collectionCorrections++
          continue
        }
        
        // 4. Appliquer les mises à jour
        if (needsUpdate) {
          await collectionInfo.collection.updateOne(
            { _id: item._id },
            { $set: updates }
          )
          collectionCorrections++
        }
      }
      
      console.log(`   ✅ ${collectionInfo.name}: ${collectionCorrections} corrections`)
      totalCorrections += collectionCorrections
    }
    
    console.log("\n📊 RÉSULTATS DU NETTOYAGE:")
    console.log("==========================")
    console.log(`📄 Items traités: ${totalItems}`)
    console.log(`🔧 Corrections appliquées: ${totalCorrections}`)
    
    const correctionRate = totalItems > 0 ? ((totalCorrections / totalItems) * 100).toFixed(1) : '0.0'
    console.log(`📈 Taux de correction: ${correctionRate}%`)
    
    if (totalCorrections > 0) {
      console.log("\n🎉 Base de données nettoyée avec succès!")
      console.log("💡 Recommandation: Exécuter 'npm run analyze-database' pour vérifier")
    } else {
      console.log("\n✅ Aucune correction nécessaire - Base de données déjà propre!")
    }

  } catch (error) {
    console.error("❌ Erreur durant le nettoyage:", error)
  } finally {
    await client.close()
  }
  
  console.log("\n🏁 Nettoyage terminé!")
}

// Fonction pour corriger les types "Unknown"
function correctType(category: string, name: string): string {
  switch (category) {
    case "sword":
      return "Sword"
    case "gun":
      return "Gun"
    case "enemy":
      // Détecter les sous-types d'ennemis
      if (name.includes("Boss") || name.includes("Admiral") || name.includes("King")) return "Boss"
      if (name.includes("Raid")) return "Raid Boss"
      return "Enemy"
    case "npc":
      // Détecter les sous-types de NPCs
      if (name.includes("Dealer") || name.includes("Shop")) return "Shop"
      if (name.includes("Quest") || name.includes("Giver")) return "Quest"
      if (name.includes("Editor") || name.includes("Teacher")) return "Misc"
      return "NPC"
    case "fruit":
      // Types de fruits par défaut (nécessiterait plus d'analyse)
      return "Natural"
    case "accessory":
      return "Accessory"
    case "material":
      return "Material"
    case "mechanic":
      return "Mechanic"
    case "quest":
      return "Quest"
    case "info":
      return "Information"
    default:
      return "Unknown"
  }
}

// Fonction pour corriger les incohérences catégorie/type
function correctCategoryType(category: string, type: string, name: string): { category: string; type: string; corrected: boolean } {
  // Corrections basées sur le type
  if (type === "Enemy" && category !== "enemy") {
    return { category: "enemy", type, corrected: true }
  }
  if (type === "Sword" && category !== "sword") {
    return { category: "sword", type, corrected: true }
  }
  if (type === "Gun" && category !== "gun") {
    return { category: "gun", type, corrected: true }
  }
  
  // Corrections basées sur le nom
  if (name.includes("Sword") && category !== "sword") {
    return { category: "sword", type: "Sword", corrected: true }
  }
  if (name.includes("Gun") && category !== "gun") {
    return { category: "gun", type: "Gun", corrected: true }
  }
  
  return { category, type, corrected: false }
}

// Fonction pour déterminer la collection correcte
function getCorrectCollection(category: string): string {
  switch (category) {
    case "fruit":
      return "fruits"
    case "sword":
      return "swords"
    case "gun":
      return "guns"
    case "enemy":
      return "enemies"
    case "npc":
      return "npcs"
    case "accessory":
      return "accessories"
    case "material":
      return "materials"
    case "mechanic":
      return "mechanics"
    case "quest":
      return "quests"
    case "raid":
      return "raids"
    default:
      return "misc"
  }
}

// Exécuter le nettoyage
cleanupDatabase().catch(console.error)
